/-
# Tutorial 02: Propositional Logic in Lean4

This tutorial covers the fundamental logical connectives and their proofs:
- Implication (→)
- Conjunction (∧, "and")
- Disjunction (∨, "or") 
- Negation (¬, "not")
- Biconditional (↔, "if and only if")

Key tactics: intro, apply, cases, constructor, left, right, exfalso
-/

import Mathlib.Tactic

/-
## 1. Implication (→)

Implication P → Q means "if P then Q".
To prove P → Q, we assume P and prove Q.
-/

-- Basic implication proof
example (P Q : Prop) : P → (Q → P) := by
  intro h_P    -- Assume P
  intro h_Q    -- Assume Q  
  exact h_P    -- We already have P

-- Modus ponens: if we have P and P → Q, then we can conclude Q
example (P Q : Prop) (h1 : P) (h2 : P → Q) : Q := by
  apply h2     -- Apply the implication P → Q
  exact h1     -- Provide the premise P

-- EXERCISE 1: Prove transitivity of implication
example (P Q R : Prop) : (P → Q) → (Q → R) → (P → R) := by
  sorry  -- Use intro three times, then apply

-- EXERCISE 2: Prove this tautology
example (P : Prop) : P → P := by
  sorry  -- Identity function

/-
## 2. Conjunction (∧)

Conjunction P ∧ Q means "P and Q".
To prove P ∧ Q, we need to prove both P and Q.
To use P ∧ Q, we can extract either component.
-/

-- Proving a conjunction using constructor
example (P Q : Prop) (h1 : P) (h2 : Q) : P ∧ Q := by
  constructor  -- Split into two goals: P and Q
  · exact h1   -- Prove P
  · exact h2   -- Prove Q

-- Alternative: using angle brackets
example (P Q : Prop) (h1 : P) (h2 : Q) : P ∧ Q := ⟨h1, h2⟩

-- Extracting from a conjunction
example (P Q : Prop) (h : P ∧ Q) : P := by
  cases h with
  | mk h_P h_Q => exact h_P

-- Shorter way using .left and .right
example (P Q : Prop) (h : P ∧ Q) : P := h.left
example (P Q : Prop) (h : P ∧ Q) : Q := h.right

-- EXERCISE 3: Prove commutativity of conjunction
example (P Q : Prop) : P ∧ Q → Q ∧ P := by
  sorry  -- Use intro, cases, constructor

-- EXERCISE 4: Prove this conjunction property
example (P Q R : Prop) : P ∧ (Q ∧ R) → (P ∧ Q) ∧ R := by
  sorry

/-
## 3. Disjunction (∨)

Disjunction P ∨ Q means "P or Q".
To prove P ∨ Q, we prove either P (using left) or Q (using right).
To use P ∨ Q, we consider both cases.
-/

-- Proving a disjunction (left case)
example (P Q : Prop) (h : P) : P ∨ Q := by
  left         -- Choose the left disjunct
  exact h

-- Proving a disjunction (right case)  
example (P Q : Prop) (h : Q) : P ∨ Q := by
  right        -- Choose the right disjunct
  exact h

-- Using a disjunction (case analysis)
example (P Q R : Prop) (h1 : P ∨ Q) (h2 : P → R) (h3 : Q → R) : R := by
  cases h1 with
  | inl h_P => exact h2 h_P  -- Case: P is true
  | inr h_Q => exact h3 h_Q  -- Case: Q is true

-- EXERCISE 5: Prove commutativity of disjunction
example (P Q : Prop) : P ∨ Q → Q ∨ P := by
  sorry  -- Use intro, cases, left/right

-- EXERCISE 6: Prove this distributive property
example (P Q R : Prop) : P ∧ (Q ∨ R) → (P ∧ Q) ∨ (P ∧ R) := by
  sorry

/-
## 4. Negation (¬)

Negation ¬P means "not P", which is defined as P → False.
To prove ¬P, we assume P and derive a contradiction (False).
-/

-- Basic negation proof
example (P : Prop) : ¬(P ∧ ¬P) := by
  intro h      -- Assume P ∧ ¬P
  cases h with
  | mk h_P h_not_P => 
    exact h_not_P h_P  -- Apply ¬P to P to get False

-- Double negation elimination (requires classical logic)
example (P : Prop) : ¬¬P → P := by
  intro h
  by_contra h_not_P  -- Assume ¬P and derive contradiction
  exact h h_not_P

-- EXERCISE 7: Prove De Morgan's law
example (P Q : Prop) : ¬(P ∨ Q) → ¬P ∧ ¬Q := by
  sorry

-- EXERCISE 8: Prove this negation property
example (P Q : Prop) : (P → Q) → (¬Q → ¬P) := by
  sorry  -- This is contraposition

/-
## 5. Biconditional (↔)

Biconditional P ↔ Q means "P if and only if Q".
It's equivalent to (P → Q) ∧ (Q → P).
-/

-- Proving a biconditional
example (P Q : Prop) : P ↔ Q ↔ (P → Q) ∧ (Q → P) := by
  constructor
  · intro h    -- Forward direction: (P ↔ Q) → (P → Q) ∧ (Q → P)
    constructor
    · exact h.mp   -- P → Q
    · exact h.mpr  -- Q → P
  · intro h    -- Backward direction: (P → Q) ∧ (Q → P) → (P ↔ Q)
    constructor
    · exact h.left   -- P → Q
    · exact h.right  -- Q → P

-- Using biconditionals
example (P Q : Prop) (h : P ↔ Q) (h_P : P) : Q := by
  exact h.mp h_P

-- EXERCISE 9: Prove reflexivity of biconditional
example (P : Prop) : P ↔ P := by
  sorry

-- EXERCISE 10: Prove symmetry of biconditional
example (P Q : Prop) : (P ↔ Q) → (Q ↔ P) := by
  sorry

/-
## 6. Truth and Falsehood

True is always provable, False is never provable.
From False, we can prove anything (principle of explosion).
-/

-- True is trivial to prove
example : True := by
  trivial

-- From False, we can prove anything
example (P : Prop) : False → P := by
  intro h
  exfalso      -- Changes goal to False
  exact h

-- EXERCISE 11: Prove these properties of True and False
example (P : Prop) : P → (True → P) := by
  sorry

example (P : Prop) : (P → False) → ¬P := by
  sorry

/-
## 7. Complex Logical Reasoning

Let's combine multiple connectives in more complex proofs.
-/

-- A complex tautology
example (P Q R : Prop) : (P → Q) → (Q → R) → (P → R) := by
  intro h1 h2 h3
  exact h2 (h1 h3)

-- Another complex example
example (P Q R : Prop) : (P ∧ Q → R) → (P → Q → R) := by
  intro h h_P h_Q
  apply h
  constructor
  · exact h_P
  · exact h_Q

-- EXERCISE 12: Prove this logical equivalence
example (P Q : Prop) : ¬(P ∧ Q) ↔ (¬P ∨ ¬Q) := by
  sorry  -- This is De Morgan's law

-- EXERCISE 13: Prove this complex statement
example (P Q R : Prop) : ((P ∨ Q) → R) → ((P → R) ∧ (Q → R)) := by
  sorry

/-
## 8. Practice Problems

Solve these problems to test your understanding of propositional logic.
-/

-- Problem 1: Prove this tautology
example (P Q : Prop) : P → (Q → P ∧ Q) := by
  sorry

-- Problem 2: Prove associativity of disjunction
example (P Q R : Prop) : (P ∨ Q) ∨ R ↔ P ∨ (Q ∨ R) := by
  sorry

-- Problem 3: Prove this distributive law
example (P Q R : Prop) : P ∨ (Q ∧ R) ↔ (P ∨ Q) ∧ (P ∨ R) := by
  sorry

-- Problem 4: Prove the law of excluded middle (requires classical logic)
example (P : Prop) : P ∨ ¬P := by
  sorry  -- Use Classical.em or by_cases

-- Problem 5: Prove this complex logical statement
example (P Q R S : Prop) : 
  ((P → Q) ∧ (R → S)) → ((P ∧ R) → (Q ∧ S)) := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. Implication (→) and the intro/apply tactics
2. Conjunction (∧) and the constructor/cases tactics
3. Disjunction (∨) and the left/right/cases tactics
4. Negation (¬) and proof by contradiction
5. Biconditional (↔) and equivalence proofs
6. Truth/False and the trivial/exfalso tactics
7. Complex logical reasoning combining multiple connectives

Next tutorial: Quantifiers and Functions.
-/
