import Mathlib.Data.Real.Basic
import Mathlib.Tactic.NormNum
import Mathlib.Analysis.Calculus.Deriv.Basic
import Mathlib.Analysis.Calculus.Deriv.Pow

/-- Compute x raised to the power n -/
def polynomial (x : ℝ) (n : ℕ) : ℝ := x^n

#check polynomial 2 4

-- Prove that it equals 16
example : polynomial 2 4 = 16 := by
  simp [polynomial]
  norm_num

-- Define the derivative of our polynomial function
def polynomial_deriv (x : ℝ) (n : ℕ) : ℝ := (n : ℝ) * x^(n-1)

-- Example: derivative of x^4 is 4x^3
example : polynomial_deriv 2 4 = 4 * 2^3 := by
  simp [polynomial_deriv]

-- Check the derivative at a specific point
#check polynomial_deriv 2 4

-- Show that our manual derivative matches the built-in one
example : polynomial_deriv 2 4 = deriv (polynomial · 4) 2 := by
  simp [polynomial_deriv, polynomial]

-- More examples of derivatives
example : polynomial_deriv 3 5 = 5 * 3^4 := by simp [polynomial_deriv]
example : polynomial_deriv 1 3 = 3 * 1^2 := by simp [polynomial_deriv]

-- The derivative of x^0 (constant 1) is 0
example : polynomial_deriv 5 0 = 0 := by simp [polynomial_deriv]
