/-
# Tutorial 01: Basic Syntax and Types in Lean4

This tutorial introduces the fundamental concepts of Lean4:
- Basic types and syntax
- Simple proofs using reflexivity
- The `simp` and `exact` tactics
- Type checking and definitional equality

Run this file in VS Code with the Lean4 extension to see interactive feedback.
-/

-- Import basic mathematical library
import Mathlib.Tactic
import Mathlib
/-
## 1. Basic Types

Lean4 has several built-in types. Let's explore the most common ones.
-/

-- Natural numbers (ℕ or Nat)
#check (5 : ℕ)
#check (0 : Nat)

-- Integers (ℤ or Int) 
#check (5 : ℤ)
#check (-3 : Int)

-- Real numbers (ℝ or Real)
#check (3.14 : ℝ)

-- Booleans
#check true
#check false

-- Propositions (statements that can be true or false)
#check (2 + 2 = 4)
#check (5 > 3)

/-
## 2. Simple Equality Proofs

The most basic proofs in Lean are equality proofs using reflexivity.
The `rfl` tactic proves that something equals itself.
-/

-- Prove that 2 + 2 equals 4
example : 2 + 2 = 4 := by
  rfl

-- Prove that 0 + n = n for any natural number n
example (n : ℕ) : 0 + n = n := by
  simp

-- EXERCISE 1: Prove that 3 * 2 = 6
example : 3 * 2 = 6 := by
  rfl  -- Replace 'sorry' with 'rfl'

-- EXERCISE 2: Prove that 1 + 1 + 1 = 3
example : 1 + 1 + 1 = 3 := by
  rfl  -- Replace 'sorry' with 'rfl'

/-
## 3. The `simp` Tactic

The `simp` tactic automatically simplifies expressions using known rules.
It's more powerful than `rfl` and can handle more complex simplifications.
-/

-- Prove that n + 0 = n (addition is commutative with 0)
example (n : ℕ) : n + 0 = n := by
  simp

-- Prove that n * 1 = n
example (n : ℕ) : n * 1 = n := by
  simp

-- Prove a more complex arithmetic identity
example (a b : ℕ) : a + b + 0 = b + a := by
  simp [add_comm]

-- EXERCISE 3: Prove that n * 0 = 0
example (n : ℕ) : n * 0 = 0 := by
  simp  -- Use simp


/-
## 4. The `exact` Tactic

The `exact` tactic provides a direct proof term.
Use it when you have exactly what you need.
-/

-- If we have a hypothesis h : P, we can prove P using exact h
example (P : Prop) (h : P) : P := by
  exact h

-- We can also use exact with expressions
example : 2 + 3 = 5 := by
  exact rfl

-- EXERCISE 5: Complete this proof using exact
example (n : ℕ) (h : n = 5) : n = 5 := by
  exact h  -- Use exact h

/-
## 5. Function Types and Lambda Expressions

Functions in Lean have types of the form A → B (read "A to B").
-/

-- Define a simple function
def double (n : ℕ) : ℕ := 2 * n

#check double
#eval double 5

-- Lambda expressions (anonymous functions)
#check fun n : ℕ => 2 * n  

-- Function composition
def add_one (n : ℕ) : ℕ := n + 1
def times_three (n : ℕ) : ℕ := 3 * n

#eval (times_three ∘ add_one) 4  -- Should be 15

-- EXERCISE 6: Define a function that squares a natural number
def square (n : ℕ) : ℕ := n^2

-- EXERCISE 7: Prove that double 3 = 6

example : double 3 = 6 := by
  rfl  -- Use rfl or simp

/-
## 6. Conditional Statements (Implications)

In Lean, "if P then Q" is written as P → Q.
-/

-- A simple implication
example (P Q : Prop) (h1 : P) (h2 : P → Q) : Q := by
  exact h2 h1

-- We can also apply functions directly
example (P Q : Prop) (h1 : P) (h2 : P → Q) : Q := h2 h1

-- EXERCISE 8: Complete this proof
example (P Q R : Prop) (h1 : P → Q) (h2 : Q → R) (h3 : P) : R := by
  exact h2 (h1 h3)  -- Apply h2 to the result of applying h1 to h3

/-
## 7. Type Checking and Definitional Equality

Lean automatically checks that types match and uses definitional equality.
-/

-- These are definitionally equal
example : (fun x => x + 0) = (fun x => x) := by
  rfl

-- Function application is definitionally equal to substitution
example : (fun x : ℕ => x + 1) 5 = 6 := by
  rfl

-- EXERCISE 9: Prove this definitional equality
example : (fun x : ℕ => 2 * x) 3 = 6 := by
  sorry

/-
## 8. Practice Problems

Try to solve these problems using the tactics you've learned:
rfl, simp, exact
-/

-- Problem 1: Basic arithmetic
example : 7 + 8 = 15 := by
  sorry

-- Problem 2: Algebraic identity
example (a : ℕ) : a + a = 2 * a := by
  sorry

-- Problem 3: Function equality
example : (fun x : ℕ => x * 2) = (fun x : ℕ => 2 * x) := by
  sorry

-- Problem 4: Implication chain
example (P Q R : Prop) (h1 : P → Q) (h2 : Q → R) : P → R := by
  sorry  -- Hint: Use intro to introduce the assumption P

-- Problem 5: Zero properties
example (n : ℕ) : 0 * n = 0 := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. Basic types in Lean4 (ℕ, ℤ, ℝ, Bool, Prop)
2. The `rfl` tactic for reflexivity proofs
3. The `simp` tactic for automatic simplification
4. The `exact` tactic for providing direct proof terms
5. Function types and lambda expressions
6. Basic implications and function application
7. Definitional equality

Next tutorial: Propositional Logic and logical connectives.
-/
