/-
# Tutorial 06: Equality and Rewriting in Lean4

This tutorial covers:
- Equality types and properties
- The rewrite tactic (rw)
- Simplification with simp and simp_rw
- Substitution and transport
- Congruence and extensionality
- Definitional vs propositional equality

Key tactics: rw, simp, simp_rw, subst, congr, ext
-/

import Mathlib.Tactic

/-
## 1. Equality Basics

Equality (=) is a fundamental relation in mathematics and logic.
In Lean, a = b is a proposition that can be proven or disproven.
-/

-- Reflexivity: everything equals itself
example (a : ℕ) : a = a := by rfl

-- Symmetry: if a = b then b = a
example (a b : ℕ) (h : a = b) : b = a := by
  rw [h]  -- Rewrite using the hypothesis

-- Alternative using symmetry
example (a b : ℕ) (h : a = b) : b = a := h.symm

-- Transitivity: if a = b and b = c then a = c
example (a b c : ℕ) (h1 : a = b) (h2 : b = c) : a = c := by
  rw [h1, h2]  -- Rewrite using both hypotheses

-- Alternative using transitivity
example (a b c : ℕ) (h1 : a = b) (h2 : b = c) : a = c := h1.trans h2

-- EXERCISE 1: Prove this equality chain
example (a b c d : ℕ) (h1 : a = b) (h2 : b = c) (h3 : c = d) : a = d := by
  sorry

-- EXERCISE 2: Use symmetry and transitivity
example (a b c : ℕ) (h1 : a = b) (h2 : c = b) : a = c := by
  sorry

/-
## 2. The Rewrite Tactic (rw)

The rw tactic is the primary tool for equality reasoning.
It replaces occurrences of the left side with the right side.
-/

-- Basic rewriting
example (a b : ℕ) (h : a = b) : a + 1 = b + 1 := by
  rw [h]  -- Replace a with b

-- Multiple rewrites
example (a b c : ℕ) (h1 : a = b) (h2 : b = c) : a + a = c + c := by
  rw [h1, h2]  -- First replace a with b, then b with c

-- Rewriting in reverse direction
example (a b : ℕ) (h : a = b) : b + 1 = a + 1 := by
  rw [← h]  -- Replace b with a (reverse direction)

-- Rewriting with arithmetic identities
example (a : ℕ) : a + 0 = a := by
  rw [Nat.add_zero]

example (a : ℕ) : 0 + a = a := by
  rw [Nat.zero_add]

-- EXERCISE 3: Use rw to prove this
example (a b c : ℕ) (h1 : a + b = c) (h2 : b = 5) : a + 5 = c := by
  sorry

-- EXERCISE 4: Use reverse rewriting
example (a b : ℕ) (h : a + 1 = b + 1) : a = b := by
  sorry  -- Hint: use Nat.add_left_cancel or linarith

/-
## 3. Simplification with simp

The simp tactic automatically applies many rewrite rules.
-/

-- Basic simplification
example (a : ℕ) : a + 0 + 0 = a := by simp

example (a b : ℕ) : a + b + 0 = b + a := by simp [add_comm]

-- Simplification with hypotheses
example (a b : ℕ) (h : a = 5) : a + b = 5 + b := by simp [h]

-- Simplification with specific lemmas
example (a b c : ℕ) : (a + b) + c = a + (b + c) := by simp [add_assoc]

-- EXERCISE 5: Use simp to prove this
example (a b : ℕ) : a * 1 + b * 1 = a + b := by
  sorry

-- EXERCISE 6: Use simp with hypotheses
example (a b c : ℕ) (h1 : a = 2) (h2 : b = 3) : a + b + c = 5 + c := by
  sorry

/-
## 4. Targeted Rewriting with simp_rw

simp_rw allows more controlled rewriting than simp.
-/

-- Rewrite only specific occurrences
example (a b : ℕ) : a + a + b = 2 * a + b := by
  simp_rw [← two_mul]

-- Multiple targeted rewrites
example (a b c : ℕ) : a + b + c + a = 2 * a + b + c := by
  simp_rw [add_comm (a + b + c) a, add_assoc, add_comm c a, ← add_assoc, ← two_mul]

-- EXERCISE 7: Use simp_rw to rearrange this expression
example (a b c : ℕ) : a + b + c = c + a + b := by
  sorry

/-
## 5. Substitution

The subst tactic substitutes a variable with its equal value.
-/

-- Basic substitution
example (a b : ℕ) (h : a = b) (h2 : a > 0) : b > 0 := by
  subst h  -- Replace all occurrences of a with b
  exact h2

-- Substitution with multiple equalities
example (a b c : ℕ) (h1 : a = b) (h2 : b = c) (h3 : a > 0) : c > 0 := by
  subst h1 h2
  exact h3

-- EXERCISE 8: Use subst to prove this
example (a b c : ℕ) (h1 : a = 5) (h2 : b = a) (h3 : c = b) : c = 5 := by
  sorry

/-
## 6. Congruence

Congruence allows us to prove equality of function applications.
-/

-- Function congruence
example (f : ℕ → ℕ) (a b : ℕ) (h : a = b) : f a = f b := by
  congr 1  -- Apply congruence
  exact h

-- Multiple argument congruence
example (f : ℕ → ℕ → ℕ) (a b c d : ℕ) (h1 : a = b) (h2 : c = d) : 
  f a c = f b d := by
  congr 1
  · exact h1
  · exact h2

-- Congruence with arithmetic
example (a b c d : ℕ) (h1 : a = b) (h2 : c = d) : a + c = b + d := by
  congr 1
  · exact h1
  · exact h2

-- EXERCISE 9: Use congr to prove this
example (a b c d : ℕ) (h1 : a = b) (h2 : c = d) : a * c = b * d := by
  sorry

/-
## 7. Extensionality

Extensionality says that functions are equal if they give equal outputs for all inputs.
-/

-- Function extensionality
example (f g : ℕ → ℕ) (h : ∀ x, f x = g x) : f = g := by
  ext x  -- Apply function extensionality
  exact h x

-- Set extensionality (sets are equal if they have the same elements)
example (s t : Set ℕ) (h : ∀ x, x ∈ s ↔ x ∈ t) : s = t := by
  ext x
  exact h x

-- EXERCISE 10: Use extensionality to prove function equality
example (f g : ℕ → ℕ) (h : ∀ x, f x + 0 = g x) : f = g := by
  sorry

/-
## 8. Definitional vs Propositional Equality

Lean distinguishes between definitional equality (rfl) and propositional equality.
-/

-- Definitional equality
example : (fun x : ℕ => x + 0) = (fun x : ℕ => x) := by rfl

example : 2 + 3 = 5 := by rfl

-- Propositional equality (requires proof)
example (a : ℕ) : a + 0 = a := by rw [Nat.add_zero]

example (a b : ℕ) : a + b = b + a := by rw [add_comm]

-- EXERCISE 11: Identify which equalities are definitional
example : (fun x : ℕ => 2 * x) 3 = 6 := by
  sorry  -- Is this rfl or does it need rw?

example (a : ℕ) : a * 1 = a := by
  sorry  -- Is this rfl or does it need rw?

/-
## 9. Equality in Different Types

Equality works differently for different types.
-/

-- Equality of pairs
example (a b c d : ℕ) (h1 : a = c) (h2 : b = d) : (a, b) = (c, d) := by
  congr 1
  · exact h1
  · exact h2

-- Equality of lists
example (a b : ℕ) (h1 : a = b) : [a, 1, 2] = [b, 1, 2] := by
  congr 1
  exact h1

-- Equality of options
example (a b : ℕ) (h : a = b) : some a = some b := by
  congr 1
  exact h

-- EXERCISE 12: Prove equality of custom types
inductive Color where
  | red | green | blue

example : Color.red = Color.red := by
  sorry

/-
## 10. Advanced Rewriting Techniques

More sophisticated uses of rewriting.
-/

-- Rewriting under binders
example (f : ℕ → ℕ) (h : ∀ x, f x = x + 1) : 
  (fun y => f y + f y) = (fun y => (y + 1) + (y + 1)) := by
  ext y
  simp [h]

-- Conditional rewriting
example (a b c : ℕ) (h : a = b) : 
  (if a > 0 then a + c else c) = (if b > 0 then b + c else c) := by
  rw [h]

-- Rewriting in hypotheses
example (a b c : ℕ) (h1 : a = b) (h2 : a + c = 10) : b + c = 10 := by
  rwa [← h1] at h2  -- Rewrite in h2 and use it

-- EXERCISE 13: Use advanced rewriting
example (f g : ℕ → ℕ) (h : f = g) (a : ℕ) : 
  (fun x => f x + a) = (fun x => g x + a) := by
  sorry

/-
## 11. Practice Problems

Test your understanding with these equality problems.
-/

-- Problem 1: Chain of equalities
example (a b c d e : ℕ) (h1 : a = b + 1) (h2 : b = c + 2) (h3 : c = d) (h4 : d = e + 3) :
  a = e + 6 := by
  sorry

-- Problem 2: Function equality with arithmetic
example (f : ℕ → ℕ) (h : ∀ x, f x = 2 * x + 1) :
  (fun x => f x + f x) = (fun x => 4 * x + 2) := by
  sorry

-- Problem 3: Equality with substitution
example (a b c : ℕ) (h1 : a + b = c) (h2 : a = 5) (h3 : b = 7) : c = 12 := by
  sorry

-- Problem 4: Complex rewriting
example (a b c d : ℕ) (h1 : a + b = c + d) (h2 : a = c) : b = d := by
  sorry

-- Problem 5: Extensionality with conditions
example (f g : ℕ → ℕ) (h : ∀ x, x > 0 → f x = g x) (h2 : f 0 = g 0) : f = g := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. Equality properties: reflexivity, symmetry, transitivity
2. The rw tactic for rewriting with equalities
3. Simplification with simp and targeted rewriting with simp_rw
4. Substitution with the subst tactic
5. Congruence for proving equality of function applications
6. Extensionality for proving function and set equality
7. Definitional vs propositional equality
8. Equality in different types (pairs, lists, options)
9. Advanced rewriting techniques
10. Complex equality proofs combining multiple techniques

Next tutorial: Sets and Relations.
-/
