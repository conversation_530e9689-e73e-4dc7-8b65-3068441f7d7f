/-
# Tutorial 05: Lists and Data Structures in Lean4

This tutorial covers:
- List basics and operations
- Pattern matching on lists
- Induction on lists
- Custom data types (inductive types)
- Trees and recursive data structures
- Proofs about data structure properties

Key tactics: cases, induction, simp, rw, constructor
-/

import Mathlib.Tactic

/-
## 1. List Basics

Lists are one of the most fundamental data structures.
In Lean, List α is the type of lists containing elements of type α.
-/

-- Basic list operations
#check List.nil        -- Empty list: []
#check List.cons       -- Add element to front: a :: l
#check [1, 2, 3, 4]    -- List literal syntax

-- List functions
#check List.length     -- Length of a list
#check List.append     -- Concatenate two lists (++)
#check List.reverse    -- Reverse a list
#check List.map        -- Apply function to each element

-- Examples
#eval [1, 2, 3].length                    -- 3
#eval [1, 2] ++ [3, 4]                   -- [1, 2, 3, 4]
#eval [1, 2, 3].reverse                  -- [3, 2, 1]
#eval [1, 2, 3].map (fun x => x * 2)     -- [2, 4, 6]

-- EXERCISE 1: Evaluate these expressions
#eval [1, 2, 3, 4].length
#eval [] ++ [1, 2, 3]
#eval [1, 2, 3].map (fun x => x + 1)

/-
## 2. Pattern Matching on Lists

Lists have two constructors: [] (nil) and :: (cons).
We can pattern match to handle these cases.
-/

-- Simple pattern matching function
def list_length {α : Type} : List α → ℕ
  | [] => 0
  | _ :: tail => 1 + list_length tail

#eval list_length [1, 2, 3, 4]  -- Should be 4

-- Pattern matching with multiple elements
def first_two {α : Type} : List α → Option (α × α)
  | [] => none
  | [_] => none
  | a :: b :: _ => some (a, b)

#eval first_two [1, 2, 3, 4]  -- some (1, 2)
#eval first_two [1]           -- none

-- EXERCISE 2: Define a function that returns the last element
def last_element {α : Type} : List α → Option α := sorry

-- EXERCISE 3: Define a function that checks if a list is empty
def is_empty {α : Type} : List α → Bool := sorry

/-
## 3. Induction on Lists

To prove properties about all lists, we use list induction.
-/

-- Prove that our length function matches the built-in one
theorem list_length_eq_length {α : Type} (l : List α) : 
  list_length l = l.length := by
  induction l with
  | nil => rfl  -- Base case: list_length [] = [].length
  | cons head tail ih =>
    -- Inductive case: list_length (head :: tail) = (head :: tail).length
    simp [list_length, List.length, ih]

-- Prove append associativity
theorem append_assoc {α : Type} (l1 l2 l3 : List α) :
  (l1 ++ l2) ++ l3 = l1 ++ (l2 ++ l3) := by
  induction l1 with
  | nil => simp
  | cons head tail ih =>
    simp [List.append, ih]

-- EXERCISE 4: Prove that appending empty list does nothing
theorem append_nil {α : Type} (l : List α) : l ++ [] = l := by
  sorry

-- EXERCISE 5: Prove length of append
theorem length_append {α : Type} (l1 l2 : List α) :
  (l1 ++ l2).length = l1.length + l2.length := by
  sorry

/-
## 4. Custom Data Types

We can define our own inductive data types.
-/

-- Simple enumeration
inductive Color where
  | red
  | green
  | blue

-- Pattern matching on custom types
def color_to_string : Color → String
  | Color.red => "Red"
  | Color.green => "Green"
  | Color.blue => "Blue"

-- Recursive data type: natural numbers
inductive MyNat where
  | zero
  | succ (n : MyNat)

-- Define addition on our natural numbers
def my_add : MyNat → MyNat → MyNat
  | MyNat.zero, n => n
  | MyNat.succ m, n => MyNat.succ (my_add m n)

-- EXERCISE 6: Define multiplication on MyNat
def my_mul : MyNat → MyNat → MyNat := sorry

-- EXERCISE 7: Define a data type for binary trees
inductive BinaryTree (α : Type) where
  | leaf
  | node (left : BinaryTree α) (value : α) (right : BinaryTree α)

/-
## 5. Trees and Recursive Structures

Let's work with binary trees in more detail.
-/

-- Tree operations
def tree_size {α : Type} : BinaryTree α → ℕ
  | BinaryTree.leaf => 0
  | BinaryTree.node left _ right => 1 + tree_size left + tree_size right

def tree_height {α : Type} : BinaryTree α → ℕ
  | BinaryTree.leaf => 0
  | BinaryTree.node left _ right => 1 + max (tree_height left) (tree_height right)

-- Tree traversal
def inorder {α : Type} : BinaryTree α → List α
  | BinaryTree.leaf => []
  | BinaryTree.node left value right => 
    inorder left ++ [value] ++ inorder right

-- Example tree
def example_tree : BinaryTree ℕ :=
  BinaryTree.node 
    (BinaryTree.node BinaryTree.leaf 1 BinaryTree.leaf)
    2
    (BinaryTree.node BinaryTree.leaf 3 BinaryTree.leaf)

#eval tree_size example_tree    -- Should be 3
#eval tree_height example_tree  -- Should be 2
#eval inorder example_tree      -- Should be [1, 2, 3]

-- EXERCISE 8: Define preorder traversal
def preorder {α : Type} : BinaryTree α → List α := sorry

-- EXERCISE 9: Define a function to count leaves
def count_leaves {α : Type} : BinaryTree α → ℕ := sorry

/-
## 6. Proofs about Data Structures

We can prove properties about our data structures.
-/

-- Prove a property about tree size
theorem tree_size_positive {α : Type} (t : BinaryTree α) (h : t ≠ BinaryTree.leaf) :
  tree_size t > 0 := by
  cases t with
  | leaf => contradiction
  | node left value right => simp [tree_size]; linarith

-- Prove relationship between inorder and tree size
theorem inorder_length_eq_size {α : Type} (t : BinaryTree α) :
  (inorder t).length = tree_size t := by
  induction t with
  | leaf => simp [inorder, tree_size]
  | node left value right ih_left ih_right =>
    simp [inorder, tree_size, List.length_append, ih_left, ih_right]
    ring

-- EXERCISE 10: Prove this property about tree height
theorem tree_height_ge_zero {α : Type} (t : BinaryTree α) : tree_height t ≥ 0 := by
  sorry

-- EXERCISE 11: Prove relationship between size and height
theorem size_le_exp_height {α : Type} (t : BinaryTree α) :
  tree_size t ≤ 2^(tree_height t) - 1 := by
  sorry

/-
## 7. Option Type and Error Handling

The Option type is used for values that might not exist.
-/

-- Option basics
#check Option.none     -- No value
#check Option.some     -- Some value

-- Safe list operations using Option
def safe_head {α : Type} : List α → Option α
  | [] => none
  | a :: _ => some a

def safe_tail {α : Type} : List α → Option (List α)
  | [] => none
  | _ :: tail => some tail

-- EXERCISE 12: Define safe indexing
def safe_get {α : Type} : List α → ℕ → Option α := sorry

-- EXERCISE 13: Prove a property about safe_head
theorem safe_head_some_iff {α : Type} (l : List α) (a : α) :
  safe_head l = some a ↔ ∃ tail, l = a :: tail := by
  sorry

/-
## 8. List Algorithms

Let's implement and prove properties of common list algorithms.
-/

-- Filter function
def my_filter {α : Type} (p : α → Bool) : List α → List α
  | [] => []
  | a :: tail => if p a then a :: my_filter p tail else my_filter p tail

-- Map function
def my_map {α β : Type} (f : α → β) : List α → List α
  | [] => []
  | a :: tail => f a :: my_map f tail

-- Fold function
def my_fold_right {α β : Type} (f : α → β → β) (init : β) : List α → β
  | [] => init
  | a :: tail => f a (my_fold_right f init tail)

-- EXERCISE 14: Implement fold_left
def my_fold_left {α β : Type} (f : β → α → β) (init : β) : List α → β := sorry

-- EXERCISE 15: Prove that map preserves length
theorem my_map_length {α β : Type} (f : α → β) (l : List α) :
  (my_map f l).length = l.length := by
  sorry

/-
## 9. Sorting and Ordering

Let's implement a simple sorting algorithm.
-/

-- Insert into sorted list
def insert_sorted (a : ℕ) : List ℕ → List ℕ
  | [] => [a]
  | b :: tail => if a ≤ b then a :: b :: tail else b :: insert_sorted a tail

-- Insertion sort
def insertion_sort : List ℕ → List ℕ
  | [] => []
  | a :: tail => insert_sorted a (insertion_sort tail)

-- EXERCISE 16: Prove that insertion sort preserves length
theorem insertion_sort_length (l : List ℕ) :
  (insertion_sort l).length = l.length := by
  sorry

/-
## 10. Practice Problems

Test your understanding with these problems.
-/

-- Problem 1: Implement list reversal
def my_reverse {α : Type} : List α → List α := sorry

-- Problem 2: Prove reverse is involutive
theorem reverse_reverse {α : Type} (l : List α) :
  my_reverse (my_reverse l) = l := by
  sorry

-- Problem 3: Implement zip function
def my_zip {α β : Type} : List α → List β → List (α × β) := sorry

-- Problem 4: Prove property about filter
theorem filter_length_le {α : Type} (p : α → Bool) (l : List α) :
  (my_filter p l).length ≤ l.length := by
  sorry

-- Problem 5: Implement and prove property about flatten
def flatten {α : Type} : List (List α) → List α := sorry

theorem flatten_length {α : Type} (ll : List (List α)) :
  (flatten ll).length = (ll.map List.length).sum := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. List basics and built-in operations
2. Pattern matching on lists and custom data types
3. List induction for proving properties
4. Defining custom inductive data types
5. Binary trees and recursive data structures
6. Proofs about data structure properties
7. Option type for safe operations
8. Common list algorithms and their properties
9. Sorting algorithms and correctness
10. Advanced list operations and proofs

Next tutorial: Equality and Rewriting.
-/
