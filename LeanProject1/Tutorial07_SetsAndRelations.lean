/-
# Tutorial 07: Sets and Relations in Lean4

This tutorial covers:
- Set theory basics and operations
- Set membership and subset proofs
- Set operations: union, intersection, complement
- Relations and their properties
- Functions as special relations
- Equivalence relations and partitions

Key tactics: intro, apply, use, obtain, ext, simp, rw
-/

import Mathlib.Tactic
import Mathlib.Data.Set.Basic

/-
## 1. Set Basics

In Lean, Set α is the type of sets containing elements of type α.
Sets are represented as predicates: s : Set α is equivalent to s : α → Prop.
-/

-- Set membership
variable (s : Set ℕ) (x : ℕ)
#check x ∈ s        -- x is in s
#check x ∉ s        -- x is not in s

-- Basic sets
#check (∅ : Set ℕ)           -- Empty set
#check (Set.univ : Set ℕ)    -- Universal set (all natural numbers)

-- Set builder notation
#check {x : ℕ | x > 5}       -- Set of natural numbers greater than 5
#check {x : ℕ | Even x}      -- Set of even natural numbers

-- Finite sets
#check ({1, 2, 3} : Set ℕ)   -- Set containing 1, 2, and 3

-- EXERCISE 1: Define these sets
def evens : Set ℕ := {x | Even x}
def odds : Set ℕ := sorry
def primes : Set ℕ := sorry

/-
## 2. Set Membership Proofs

To prove x ∈ s, we need to prove the predicate s x.
-/

-- Membership in explicit sets
example : 2 ∈ ({1, 2, 3} : Set ℕ) := by simp

example : 4 ∈ {x : ℕ | Even x} := by
  simp [Even]
  use 2
  rfl

-- Non-membership
example : 3 ∉ {x : ℕ | Even x} := by
  simp [Even]
  intro k h
  -- 3 = 2 * k is impossible for natural k
  linarith

-- EXERCISE 2: Prove these membership statements
example : 5 ∈ {x : ℕ | x > 3} := by
  sorry

example : 0 ∉ {x : ℕ | x > 0} := by
  sorry

/-
## 3. Subset Relations

s ⊆ t means every element of s is also in t.
-/

-- Basic subset proof
example : {1, 2} ⊆ {1, 2, 3} := by
  intro x hx
  simp at hx ⊢
  cases hx with
  | inl h => left; exact h
  | inr h => right; left; exact h

-- Subset with predicates
example : {x : ℕ | x > 5} ⊆ {x : ℕ | x > 3} := by
  intro x hx
  simp at hx ⊢
  linarith [hx]

-- Subset transitivity
example (s t u : Set ℕ) (h1 : s ⊆ t) (h2 : t ⊆ u) : s ⊆ u := by
  intro x hx
  exact h2 (h1 hx)

-- EXERCISE 3: Prove these subset relations
example : {x : ℕ | x > 10} ⊆ {x : ℕ | x > 5} := by
  sorry

example : evens ⊆ {x : ℕ | x ≥ 0} := by
  sorry

/-
## 4. Set Operations

Sets support union (∪), intersection (∩), and complement (ᶜ).
-/

-- Union
example (s t : Set ℕ) (x : ℕ) : x ∈ s ∪ t ↔ x ∈ s ∨ x ∈ t := by rfl

example : {1, 2} ∪ {2, 3} = {1, 2, 3} := by
  ext x
  simp
  constructor
  · intro h
    cases h with
    | inl h => cases h with
      | inl h => left; exact h
      | inr h => right; left; exact h
    | inr h => cases h with
      | inl h => right; left; exact h
      | inr h => right; right; exact h
  · intro h
    cases h with
    | inl h => left; left; exact h
    | inr h => cases h with
      | inl h => left; right; exact h
      | inr h => right; right; exact h

-- Intersection
example (s t : Set ℕ) (x : ℕ) : x ∈ s ∩ t ↔ x ∈ s ∧ x ∈ t := by rfl

example : {1, 2, 3} ∩ {2, 3, 4} = {2, 3} := by
  ext x
  simp
  constructor
  · intro ⟨h1, h2⟩
    cases h1 with
    | inl h => cases h2 with
      | inl h' => contradiction
      | inr h' => cases h' with
        | inl h'' => contradiction
        | inr h'' => contradiction
    | inr h => cases h with
      | inl h' => cases h2 with
        | inl h'' => contradiction
        | inr h'' => cases h'' with
          | inl h''' => left; exact h'
          | inr h''' => contradiction
      | inr h' => cases h2 with
        | inl h'' => contradiction
        | inr h'' => cases h'' with
          | inl h''' => contradiction
          | inr h''' => right; exact h'
  · intro h
    cases h with
    | inl h => constructor
      · right; left; exact h
      · right; left; exact h
    | inr h => constructor
      · right; right; exact h
      · right; right; exact h

-- EXERCISE 4: Prove these set operation properties
example (s t : Set ℕ) : s ∪ t = t ∪ s := by
  sorry

example (s t : Set ℕ) : s ∩ t = t ∩ s := by
  sorry

/-
## 5. Set Identities

Important identities involving set operations.
-/

-- De Morgan's laws
example (s t : Set ℕ) : (s ∪ t)ᶜ = sᶜ ∩ tᶜ := by
  ext x
  simp
  push_neg
  rfl

example (s t : Set ℕ) : (s ∩ t)ᶜ = sᶜ ∪ tᶜ := by
  ext x
  simp
  push_neg
  rfl

-- Distributive laws
example (s t u : Set ℕ) : s ∩ (t ∪ u) = (s ∩ t) ∪ (s ∩ u) := by
  ext x
  simp
  constructor
  · intro ⟨hx_s, hx_tu⟩
    cases hx_tu with
    | inl hx_t => left; exact ⟨hx_s, hx_t⟩
    | inr hx_u => right; exact ⟨hx_s, hx_u⟩
  · intro h
    cases h with
    | inl ⟨hx_s, hx_t⟩ => exact ⟨hx_s, Or.inl hx_t⟩
    | inr ⟨hx_s, hx_u⟩ => exact ⟨hx_s, Or.inr hx_u⟩

-- EXERCISE 5: Prove the other distributive law
example (s t u : Set ℕ) : s ∪ (t ∩ u) = (s ∪ t) ∩ (s ∪ u) := by
  sorry

/-
## 6. Relations

A relation on type α is a subset of α × α.
-/

-- Define a relation
def divides : ℕ → ℕ → Prop := fun a b => ∃ k, b = a * k

-- Relation properties
def reflexive (r : ℕ → ℕ → Prop) : Prop := ∀ x, r x x
def symmetric (r : ℕ → ℕ → Prop) : Prop := ∀ x y, r x y → r y x
def transitive (r : ℕ → ℕ → Prop) : Prop := ∀ x y z, r x y → r y z → r x z

-- Prove properties of divides relation
example : reflexive divides := by
  intro x
  use 1
  simp

example : transitive divides := by
  intro x y z hxy hyz
  obtain ⟨k1, hk1⟩ := hxy
  obtain ⟨k2, hk2⟩ := hyz
  use k1 * k2
  rw [hk2, hk1]
  ring

-- EXERCISE 6: Define and prove properties of other relations
def less_than_or_equal : ℕ → ℕ → Prop := fun a b => a ≤ b

example : reflexive less_than_or_equal := by
  sorry

example : transitive less_than_or_equal := by
  sorry

/-
## 7. Equivalence Relations

An equivalence relation is reflexive, symmetric, and transitive.
-/

-- Define equivalence relation
def equivalence_relation (r : ℕ → ℕ → Prop) : Prop :=
  reflexive r ∧ symmetric r ∧ transitive r

-- Modular arithmetic as equivalence relation
def mod_equiv (n : ℕ) : ℕ → ℕ → Prop := fun a b => a % n = b % n

-- Prove mod_equiv is an equivalence relation
example (n : ℕ) (hn : n > 0) : equivalence_relation (mod_equiv n) := by
  constructor
  · -- Reflexive
    intro x
    rfl
  constructor
  · -- Symmetric
    intro x y h
    exact h.symm
  · -- Transitive
    intro x y z hxy hyz
    exact hxy.trans hyz

-- EXERCISE 7: Define another equivalence relation
def same_parity : ℕ → ℕ → Prop := fun a b => Even a ↔ Even b

example : equivalence_relation same_parity := by
  sorry

/-
## 8. Functions as Relations

Functions can be viewed as special relations.
-/

-- A relation is functional if it's single-valued
def functional (r : ℕ → ℕ → Prop) : Prop :=
  ∀ x y z, r x y → r x z → y = z

-- A relation is total if every input has an output
def total (r : ℕ → ℕ → Prop) : Prop :=
  ∀ x, ∃ y, r x y

-- Function graph
def function_graph (f : ℕ → ℕ) : ℕ → ℕ → Prop := fun x y => y = f x

-- Prove function graph is functional and total
example (f : ℕ → ℕ) : functional (function_graph f) := by
  intro x y z hy hz
  simp [function_graph] at hy hz
  exact hy.trans hz.symm

example (f : ℕ → ℕ) : total (function_graph f) := by
  intro x
  use f x
  rfl

-- EXERCISE 8: Prove properties of function composition
def compose_relation (r s : ℕ → ℕ → Prop) : ℕ → ℕ → Prop :=
  fun x z => ∃ y, r x y ∧ s y z

example (r s : ℕ → ℕ → Prop) (hr : functional r) (hs : functional s) :
  functional (compose_relation r s) := by
  sorry

/-
## 9. Inverse Relations and Images

Relations have inverses and can map sets to sets.
-/

-- Inverse relation
def inverse_relation (r : ℕ → ℕ → Prop) : ℕ → ℕ → Prop := fun y x => r x y

-- Image of a set under a relation
def relation_image (r : ℕ → ℕ → Prop) (s : Set ℕ) : Set ℕ :=
  {y | ∃ x ∈ s, r x y}

-- Preimage of a set under a relation
def relation_preimage (r : ℕ → ℕ → Prop) (t : Set ℕ) : Set ℕ :=
  {x | ∃ y ∈ t, r x y}

-- EXERCISE 9: Prove properties of images
example (r : ℕ → ℕ → Prop) (s t : Set ℕ) :
  relation_image r (s ∪ t) = relation_image r s ∪ relation_image r t := by
  sorry

/-
## 10. Practice Problems

Test your understanding with these problems.
-/

-- Problem 1: Set equality
example : {x : ℕ | x^2 < 10} = {0, 1, 2, 3} := by
  sorry

-- Problem 2: Complex set operations
example (s t u : Set ℕ) : (s ∪ t) ∩ (s ∪ u) = s ∪ (t ∩ u) := by
  sorry

-- Problem 3: Relation composition
def composition (r s : ℕ → ℕ → Prop) : ℕ → ℕ → Prop :=
  fun x z => ∃ y, r x y ∧ s y z

example (r : ℕ → ℕ → Prop) (hr : transitive r) :
  composition r r = r := by
  sorry

-- Problem 4: Equivalence classes
def equivalence_class (r : ℕ → ℕ → Prop) (x : ℕ) : Set ℕ :=
  {y | r x y}

example (r : ℕ → ℕ → Prop) (hr : equivalence_relation r) (x y : ℕ) :
  r x y → equivalence_class r x = equivalence_class r y := by
  sorry

-- Problem 5: Function injectivity and surjectivity
def injective (f : ℕ → ℕ) : Prop := ∀ x y, f x = f y → x = y
def surjective (f : ℕ → ℕ) : Prop := ∀ y, ∃ x, f x = y

example (f : ℕ → ℕ) (hf : injective f) (s t : Set ℕ) :
  f '' (s ∩ t) = f '' s ∩ f '' t := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. Set theory basics and membership proofs
2. Subset relations and their properties
3. Set operations: union, intersection, complement
4. Important set identities and De Morgan's laws
5. Relations and their properties (reflexive, symmetric, transitive)
6. Equivalence relations and their significance
7. Functions as special relations
8. Inverse relations and set images
9. Complex proofs involving sets and relations
10. Applications to modular arithmetic and function properties

Next tutorial: Advanced Tactics and Proof Techniques.
-/
