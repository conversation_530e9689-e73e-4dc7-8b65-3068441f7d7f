/-
# Tutorial 08: Advanced Tactics and Proof Techniques in Lean4

This tutorial covers:
- Intermediate steps with `have` and `suffices`
- Proof by contradiction with `by_contra`
- Classical reasoning and excluded middle
- Case analysis with `by_cases`
- Working with assumptions using `specialize`
- Advanced goal manipulation
- Proof automation and decision procedures

Key tactics: have, suffices, by_contra, by_cases, specialize, classical, tauto
-/

import Mathlib.Tactic
import Mathlib.Logic.Basic

/-
## 1. Intermediate Steps with `have`

The `have` tactic allows you to prove intermediate results within a proof.
-/

-- Basic use of have
example (a b c : ℕ) (h1 : a = b) (h2 : b = c) : a = c := by
  have h3 : a = b := h1  -- Intermediate step (redundant here)
  have h4 : b = c := h2  -- Another intermediate step
  exact h3.trans h4

-- More useful example
example (n : ℕ) : n^2 + 2*n + 1 = (n + 1)^2 := by
  have h : (n + 1)^2 = (n + 1) * (n + 1) := by ring
  rw [← h]
  ring

-- Have with complex intermediate results
example (a b : ℝ) (h : a > 0) (h2 : b > 0) : (a + b)^2 > a^2 := by
  have h3 : (a + b)^2 = a^2 + 2*a*b + b^2 := by ring
  have h4 : 2*a*b > 0 := by
    apply mul_pos
    · apply mul_pos
      · norm_num
      · exact h
    · exact h2
  have h5 : b^2 ≥ 0 := sq_nonneg b
  linarith [h3, h4, h5]

-- EXERCISE 1: Use have to break down this proof
example (x y : ℝ) (hx : x > 1) (hy : y > 1) : x * y > 1 := by
  sorry  -- Use have to show x > 0 and y > 0 first

/-
## 2. Backward Reasoning with `suffices`

The `suffices` tactic allows you to state what would be sufficient to prove the goal.
-/

-- Basic suffices
example (a b : ℕ) (h : a = b) : a + 1 = b + 1 := by
  suffices h2 : a = b by
    rw [h2]
  exact h

-- More complex example
example (n : ℕ) : n^2 ≥ n := by
  suffices h : n * n ≥ n * 1 by
    simp at h
    exact h
  apply Nat.mul_le_mul_left
  simp

-- Suffices with multiple goals
example (a b c : ℝ) (h1 : a ≤ b) (h2 : b ≤ c) : a ≤ c := by
  suffices h3 : a ≤ b ∧ b ≤ c by
    exact le_trans h3.1 h3.2
  exact ⟨h1, h2⟩

-- EXERCISE 2: Use suffices to prove this
example (x : ℝ) (h : x^2 = 4) : x = 2 ∨ x = -2 := by
  sorry  -- Use suffices to reduce to showing (x - 2) * (x + 2) = 0

/-
## 3. Proof by Contradiction with `by_contra`

The `by_contra` tactic assumes the negation of the goal and derives a contradiction.
-/

-- Basic contradiction
example (p : Prop) : ¬¬p → p := by
  intro h
  by_contra h_not_p
  exact h h_not_p

-- Contradiction with natural numbers
example (n : ℕ) : ¬(n < n) := by
  by_contra h
  exact Nat.lt_irrefl n h

-- More complex contradiction
example (a b : ℝ) (h : a^2 + b^2 = 0) : a = 0 ∧ b = 0 := by
  constructor
  · by_contra h_not_a
    have h1 : a^2 > 0 := sq_pos_of_ne_zero _ h_not_a
    have h2 : b^2 ≥ 0 := sq_nonneg b
    linarith [h, h1, h2]
  · by_contra h_not_b
    have h1 : a^2 ≥ 0 := sq_nonneg a
    have h2 : b^2 > 0 := sq_pos_of_ne_zero _ h_not_b
    linarith [h, h1, h2]

-- EXERCISE 3: Use by_contra to prove this
example (x : ℝ) (h : x^2 < 0) : False := by
  sorry  -- Show this is impossible

/-
## 4. Classical Reasoning and Excluded Middle

Classical logic allows us to use the law of excluded middle: P ∨ ¬P.
-/

-- Law of excluded middle
example (p : Prop) : p ∨ ¬p := Classical.em p

-- Using classical reasoning
example (p q : Prop) : ¬(p ∧ q) → (¬p ∨ ¬q) := by
  intro h
  by_cases hp : p
  · right
    intro hq
    exact h ⟨hp, hq⟩
  · left
    exact hp

-- Double negation elimination
example (p : Prop) : ¬¬p → p := by
  intro h
  by_contra h_not_p
  exact h h_not_p

-- EXERCISE 4: Use classical reasoning
example (p q : Prop) : (p → q) → (¬q → ¬p) := by
  sorry  -- This is contraposition

/-
## 5. Case Analysis with `by_cases`

The `by_cases` tactic splits on whether a proposition is true or false.
-/

-- Basic case analysis
example (n : ℕ) : n = 0 ∨ n > 0 := by
  by_cases h : n = 0
  · left; exact h
  · right; exact Nat.pos_of_ne_zero h

-- Case analysis with complex conditions
example (x : ℝ) : |x| = x ∨ |x| = -x := by
  by_cases h : x ≥ 0
  · left; exact abs_of_nonneg h
  · right; exact abs_of_neg (not_le.mp h)

-- Multiple case analysis
example (a b : ℕ) : a ≤ b ∨ b < a := by
  by_cases h : a ≤ b
  · left; exact h
  · right; exact Nat.lt_of_not_ge h

-- EXERCISE 5: Use by_cases to prove this
example (x y : ℝ) : max x y = x ∨ max x y = y := by
  sorry

/-
## 6. Working with Assumptions using `specialize`

The `specialize` tactic applies a universal statement to specific values.
-/

-- Basic specialization
example (h : ∀ n : ℕ, n + 0 = n) : 5 + 0 = 5 := by
  specialize h 5
  exact h

-- Specialization with multiple arguments
example (h : ∀ a b : ℕ, a + b = b + a) : 3 + 7 = 7 + 3 := by
  specialize h 3 7
  exact h

-- Specialization in complex proofs
example (f : ℕ → ℕ) (h : ∀ n, f n > n) : f 10 > 10 ∧ f 20 > 20 := by
  constructor
  · specialize h 10; exact h
  · specialize h 20; exact h

-- EXERCISE 6: Use specialize effectively
example (h : ∀ x : ℝ, x^2 ≥ 0) (a : ℝ) : a^2 + 1 ≥ 1 := by
  sorry

/-
## 7. Advanced Goal Manipulation

Techniques for restructuring and simplifying goals.
-/

-- Converting between equivalent forms
example (p q : Prop) : (p → q) ↔ (¬p ∨ q) := by
  constructor
  · intro h
    by_cases hp : p
    · right; exact h hp
    · left; exact hp
  · intro h hp
    cases h with
    | inl h_not_p => contradiction
    | inr hq => exact hq

-- Working with complex logical structures
example (p q r : Prop) : (p ∧ q) → r ↔ p → (q → r) := by
  constructor
  · intro h hp hq
    exact h ⟨hp, hq⟩
  · intro h ⟨hp, hq⟩
    exact h hp hq

-- EXERCISE 7: Prove this logical equivalence
example (p q : Prop) : ¬(p ∨ q) ↔ (¬p ∧ ¬q) := by
  sorry

/-
## 8. Proof Automation and Decision Procedures

Lean provides powerful automation for certain types of problems.
-/

-- Linear arithmetic
example (a b c : ℝ) (h1 : a ≤ b) (h2 : b ≤ c) (h3 : c ≤ a + 1) : a ≤ c ∧ c ≤ a + 1 := by
  constructor
  · linarith [h1, h2]
  · exact h3

-- Ring operations
example (a b : ℝ) : (a + b)^2 = a^2 + 2*a*b + b^2 := by ring

-- Field operations
example (a b : ℝ) (h : b ≠ 0) : a / b * b = a := by field_simp

-- Tautology checker
example (p q : Prop) : (p → q) → (¬q → ¬p) := by tauto

-- EXERCISE 8: Use automation tactics
example (x y z : ℝ) (h1 : x + y = 10) (h2 : y + z = 15) (h3 : x + z = 12) : 
  x = 3.5 ∧ y = 6.5 ∧ z = 8.5 := by
  sorry  -- Use linarith

/-
## 9. Combining Advanced Tactics

Real proofs often combine multiple advanced techniques.
-/

-- Complex proof combining multiple tactics
theorem intermediate_value_discrete (f : ℕ → ℤ) (a b : ℕ) (h1 : a < b) 
  (h2 : f a < 0) (h3 : f b > 0) : ∃ c, a < c ∧ c < b ∧ f c = 0 ∨ 
  (f c < 0 ∧ f (c + 1) > 0) := by
  -- This is a simplified version - real intermediate value theorem is more complex
  by_cases h : ∃ c, a < c ∧ c < b ∧ f c = 0
  · left; exact h
  · right
    -- Use strong induction or well-ordering to find the transition point
    sorry

-- Proof using multiple advanced tactics
example (x y : ℝ) (h : x^2 + y^2 = 1) : -1 ≤ x ∧ x ≤ 1 := by
  constructor
  · by_contra h_not
    push_neg at h_not
    have h1 : x^2 > 1 := by
      by_cases hx : x ≥ 0
      · have : x > 1 := by linarith [h_not]
        exact one_lt_sq_iff.mpr this
      · have : x < -1 := by linarith [h_not]
        rw [← neg_neg x]
        have : -x > 1 := by linarith
        rw [neg_sq]
        exact one_lt_sq_iff.mpr this
    have h2 : y^2 ≥ 0 := sq_nonneg y
    linarith [h, h1, h2]
  · by_contra h_not
    push_neg at h_not
    have h1 : x^2 > 1 := one_lt_sq_iff.mpr h_not
    have h2 : y^2 ≥ 0 := sq_nonneg y
    linarith [h, h1, h2]

-- EXERCISE 9: Combine multiple tactics
example (a b c : ℝ) (h1 : a + b + c = 0) (h2 : a^2 + b^2 + c^2 = 1) : 
  ∃ x ∈ ({a, b, c} : Set ℝ), |x| ≥ 1/Real.sqrt 3 := by
  sorry

/-
## 10. Practice Problems

Test your mastery of advanced tactics.
-/

-- Problem 1: Complex contradiction
example (x : ℝ) : ¬(x^2 + 1 < 0) := by
  sorry

-- Problem 2: Classical reasoning with quantifiers
example : ¬(∀ x : ℝ, x^2 ≥ x) := by
  sorry

-- Problem 3: Case analysis with arithmetic
example (n : ℕ) : n^2 ≥ n := by
  sorry

-- Problem 4: Combining have and suffices
example (a b : ℝ) (h : a^2 + b^2 = 0) : a = 0 ∧ b = 0 := by
  sorry

-- Problem 5: Advanced logical reasoning
example (p q r : Prop) : ((p → q) ∧ (q → r)) → (p → r) := by
  sorry

-- Problem 6: Proof by contradiction with arithmetic
example (x y : ℝ) (h1 : x + y = 5) (h2 : x * y = 7) : x^2 + y^2 = 11 := by
  sorry

-- Problem 7: Classical logic and De Morgan
example (p q : Prop) : ¬(p ∧ q) ↔ (¬p ∨ ¬q) := by
  sorry

-- Problem 8: Complex case analysis
example (f : ℝ → ℝ) (h : ∀ x, f (f x) = x) : ∀ x, f x = x ∨ f (f (f x)) = f x := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. Using `have` for intermediate steps and clearer proofs
2. Backward reasoning with `suffices` to structure proofs
3. Proof by contradiction with `by_contra`
4. Classical reasoning and the law of excluded middle
5. Case analysis with `by_cases` for handling different scenarios
6. Working with assumptions using `specialize`
7. Advanced goal manipulation techniques
8. Proof automation with linarith, ring, field_simp, and tauto
9. Combining multiple advanced tactics in complex proofs
10. Real-world applications of advanced proof techniques

These advanced tactics will help you tackle more sophisticated mathematical proofs
and handle complex logical reasoning in Lean4. Practice combining them to build
powerful and elegant proofs.
-/
