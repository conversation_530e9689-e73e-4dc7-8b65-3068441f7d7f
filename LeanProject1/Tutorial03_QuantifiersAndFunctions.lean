/-
# Tutorial 03: Quantifiers and Functions in Lean4

This tutorial covers:
- Universal quantification (∀, "for all")
- Existential quantification (∃, "there exists")
- Function definitions and applications
- Lambda expressions and higher-order functions
- Dependent types and type families

Key tactics: intro, apply, use, obtain, specialize
-/

import Mathlib.Tactic

/-
## 1. Universal Quantification (∀)

Universal quantification ∀ x, P(x) means "for all x, P(x) holds".
To prove ∀ x, P(x), we introduce an arbitrary x and prove P(x).
To use ∀ x, P(x), we can apply it to any specific value.
-/

-- Basic universal quantification
example : ∀ n : ℕ, n + 0 = n := by
  intro n      -- Introduce arbitrary n
  rfl          -- Prove n + 0 = n by reflexivity

-- Using a universal statement
example (h : ∀ n : ℕ, n + 0 = n) : 5 + 0 = 5 := by
  exact h 5    -- Apply h to the specific value 5

-- Alternative: using specialize
example (h : ∀ n : ℕ, n + 0 = n) : 5 + 0 = 5 := by
  specialize h 5  -- Specialize h to n = 5
  exact h

-- EXERCISE 1: Prove this universal statement
example : ∀ n : ℕ, 0 + n = n := by
  sorry

-- EXERCISE 2: Use the universal statement to prove a specific case
example (h : ∀ n : ℕ, n * 1 = n) : 7 * 1 = 7 := by
  sorry

/-
## 2. Existential Quantification (∃)

Existential quantification ∃ x, P(x) means "there exists x such that P(x)".
To prove ∃ x, P(x), we provide a witness and prove P holds for that witness.
To use ∃ x, P(x), we extract the witness and the proof.
-/

-- Basic existential proof
example : ∃ n : ℕ, n + 2 = 5 := by
  use 3        -- Provide witness n = 3
  rfl          -- Prove 3 + 2 = 5

-- Alternative syntax
example : ∃ n : ℕ, n + 2 = 5 := ⟨3, rfl⟩

-- Using an existential statement
example (h : ∃ n : ℕ, n + 2 = 5) : ∃ m : ℕ, m < 10 := by
  obtain ⟨n, hn⟩ := h  -- Extract witness n and proof hn : n + 2 = 5
  use n                 -- Use the same witness
  -- Now we need to prove n < 10
  -- Since n + 2 = 5, we have n = 3, so n < 10
  linarith [hn]

-- EXERCISE 3: Prove this existential statement
example : ∃ n : ℕ, n * n = 16 := by
  sorry  -- Use 4 as witness

-- EXERCISE 4: Use an existential to prove another existential
example (h : ∃ n : ℕ, n > 10) : ∃ m : ℕ, m > 5 := by
  sorry

/-
## 3. Combining Quantifiers

We can combine universal and existential quantifiers in various ways.
-/

-- For all x, there exists y such that y > x
example : ∀ x : ℕ, ∃ y : ℕ, y > x := by
  intro x      -- Introduce arbitrary x
  use x + 1    -- Provide witness y = x + 1
  linarith     -- Prove x + 1 > x

-- There exists x such that for all y, some property holds
example : ∃ x : ℕ, ∀ y : ℕ, x ≤ y := by
  use 0        -- Provide witness x = 0
  intro y      -- Introduce arbitrary y
  exact Nat.zero_le y  -- 0 ≤ y for any natural number y

-- EXERCISE 5: Prove this mixed quantifier statement
example : ∀ x : ℕ, ∃ y : ℕ, x + y = 10 := by
  sorry

-- EXERCISE 6: Prove this statement about existence
example : ∃ x : ℕ, ∀ y : ℕ, x + y = y := by
  sorry  -- Hint: what number has the property that x + y = y for all y?

/-
## 4. Function Definitions

Functions are first-class objects in Lean. We can define them and prove properties about them.
-/

-- Simple function definition
def double (n : ℕ) : ℕ := 2 * n

-- Prove a property about our function
example : ∀ n : ℕ, double n = 2 * n := by
  intro n
  rfl  -- This follows from the definition

-- Function with multiple arguments
def add_then_multiply (a b c : ℕ) : ℕ := (a + b) * c

-- Prove a property about this function
example : add_then_multiply 2 3 4 = 20 := by
  rfl

-- EXERCISE 7: Define a function that computes n²
def square (n : ℕ) : ℕ := sorry

-- EXERCISE 8: Prove that square 5 = 25
example : square 5 = 25 := by
  sorry

/-
## 5. Lambda Expressions

Lambda expressions create anonymous functions.
-/

-- Basic lambda expression
#check fun n : ℕ => n + 1
#check (λ n : ℕ => n + 1)  -- Alternative syntax

-- Lambda with multiple arguments
#check fun a b : ℕ => a + b

-- Using lambdas in proofs
example : (fun n : ℕ => n + 0) = (fun n : ℕ => n) := by
  ext n  -- Extensionality: prove functions are equal by showing they're equal on all inputs
  simp

-- EXERCISE 9: Prove this lambda equality
example : (fun n : ℕ => 2 * n) = (fun n : ℕ => n + n) := by
  sorry

/-
## 6. Higher-Order Functions

Functions can take other functions as arguments or return functions.
-/

-- Function that takes a function as argument
def apply_twice (f : ℕ → ℕ) (n : ℕ) : ℕ := f (f n)

-- Example usage
#eval apply_twice (fun x => x + 1) 5  -- Should be 7

-- Prove a property about higher-order functions
example : apply_twice double 3 = 12 := by
  rfl

-- Function composition
def compose (f g : ℕ → ℕ) : ℕ → ℕ := fun x => f (g x)

-- EXERCISE 10: Prove this property about function composition
example (f g : ℕ → ℕ) (x : ℕ) : compose f g x = f (g x) := by
  sorry

/-
## 7. Dependent Types

In Lean, types can depend on values. This enables very expressive type systems.
-/

-- A type family: vectors of length n
-- (We'll use List for simplicity, but imagine it's length-indexed)
def Vec (α : Type) (n : ℕ) := {l : List α // l.length = n}

-- A dependent function: repeat an element n times
def repeat_n (α : Type) (n : ℕ) (a : α) : Vec α n := by
  use List.replicate n a
  exact List.length_replicate n a

-- EXERCISE 11: Define a dependent function type
-- A function that takes n : ℕ and returns a function ℕ → ℕ that adds n
def add_n : ℕ → (ℕ → ℕ) := sorry

/-
## 8. Quantifiers with Predicates

We can use quantifiers with more complex predicates.
-/

-- Define a predicate
def Even (n : ℕ) : Prop := ∃ k : ℕ, n = 2 * k

-- Prove something about all even numbers
example : ∀ n : ℕ, Even n → Even (n + 2) := by
  intro n h
  obtain ⟨k, hk⟩ := h  -- n = 2 * k
  use k + 1             -- Witness for n + 2 = 2 * (k + 1)
  linarith [hk]         -- Arithmetic

-- EXERCISE 12: Define the predicate "Odd" and prove a property
def Odd (n : ℕ) : Prop := sorry

example : ∀ n : ℕ, Odd n → Odd (n + 2) := by
  sorry

/-
## 9. Existential Uniqueness

Sometimes we want to say "there exists a unique x such that P(x)".
-/

-- Existential uniqueness: ∃! x, P(x)
example : ∃! n : ℕ, n + 5 = 8 := by
  use 3
  constructor
  · rfl  -- Prove 3 + 5 = 8
  · intro m hm  -- Prove uniqueness: if m + 5 = 8, then m = 3
    linarith [hm]

-- EXERCISE 13: Prove existential uniqueness
example : ∃! n : ℕ, 2 * n = 10 := by
  sorry

/-
## 10. Practice Problems

Test your understanding with these problems.
-/

-- Problem 1: Mixed quantifiers
example : ∀ x : ℕ, ∃ y : ℕ, x < y ∧ y < x + 2 := by
  sorry

-- Problem 2: Function properties
def triple (n : ℕ) : ℕ := 3 * n

example : ∀ n : ℕ, triple n > 2 * n := by
  sorry

-- Problem 3: Existential with conditions
example : ∃ n : ℕ, n > 5 ∧ Even n := by
  sorry

-- Problem 4: Universal with implication
example : ∀ n : ℕ, n > 0 → ∃ m : ℕ, m * m = n ∨ m * m < n ∧ (m + 1) * (m + 1) > n := by
  sorry  -- This is about square roots

-- Problem 5: Higher-order function property
def iterate (f : ℕ → ℕ) : ℕ → ℕ → ℕ
  | 0, x => x
  | n + 1, x => f (iterate f n x)

example : iterate (fun x => x + 1) 3 0 = 3 := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. Universal quantification (∀) with intro and apply
2. Existential quantification (∃) with use and obtain
3. Function definitions and properties
4. Lambda expressions and anonymous functions
5. Higher-order functions and function composition
6. Dependent types and type families
7. Predicates and quantified statements
8. Existential uniqueness (∃!)

Next tutorial: Natural Numbers and Induction.
-/
