# Lean4 Tutorial Series: Learning Through Proofs

This directory contains a comprehensive set of Lean4 tutorials designed to teach the fundamentals of the language through proving common mathematical statements. Each tutorial builds upon the previous ones, gradually introducing more advanced concepts and proof techniques.

## Prerequisites

- Install Lean4 and the Lean4 VS Code extension
- Basic familiarity with mathematical concepts
- No prior experience with proof assistants required

## Tutorial Overview

### Tutorial 01: Basic Syntax and Types
**File:** `Tutorial01_BasicSyntaxAndTypes.lean`

**Topics Covered:**
- Basic types (ℕ, ℤ, ℝ, Bool, Prop)
- Simple equality proofs using `rfl`
- The `simp` and `exact` tactics
- Function types and lambda expressions
- Definitional equality

**Key Learning:** Master the fundamental building blocks of Lean4 and prove simple arithmetic identities.

### Tutorial 02: Propositional Logic
**File:** `Tutorial02_PropositionalLogic.lean`

**Topics Covered:**
- Logical connectives: implication (→), conjunction (∧), disjunction (∨)
- Negation (¬) and biconditional (↔)
- Truth and falsehood
- Key tactics: `intro`, `apply`, `cases`, `constructor`, `left`, `right`

**Key Learning:** Understand how to construct and deconstruct logical statements using <PERSON>n's proof tactics.

### Tutorial 03: Quantifiers and Functions
**File:** `Tutorial03_QuantifiersAndFunctions.lean`

**Topics Covered:**
- Universal quantification (∀) and existential quantification (∃)
- Function definitions and higher-order functions
- Lambda expressions and function composition
- Dependent types introduction
- Key tactics: `use`, `obtain`, `specialize`

**Key Learning:** Work with quantified statements and understand how functions are treated as first-class objects.

### Tutorial 04: Natural Numbers and Induction
**File:** `Tutorial04_NaturalNumbersAndInduction.lean`

**Topics Covered:**
- Natural number arithmetic and properties
- Mathematical induction proofs
- Recursive function definitions
- Strong induction and well-founded recursion
- Common induction patterns

**Key Learning:** Master proof by induction, the fundamental technique for reasoning about natural numbers.

### Tutorial 05: Lists and Data Structures
**File:** `Tutorial05_ListsAndDataStructures.lean`

**Topics Covered:**
- List operations and pattern matching
- Induction on lists
- Custom inductive data types
- Binary trees and recursive structures
- Proofs about data structure properties

**Key Learning:** Define and reason about custom data types and their properties.

### Tutorial 06: Equality and Rewriting
**File:** `Tutorial06_EqualityAndRewriting.lean`

**Topics Covered:**
- Equality properties and the `rw` tactic
- Simplification with `simp` and `simp_rw`
- Substitution and congruence
- Extensionality principles
- Definitional vs propositional equality

**Key Learning:** Master the art of rewriting and equality reasoning in mathematical proofs.

### Tutorial 07: Sets and Relations
**File:** `Tutorial07_SetsAndRelations.lean`

**Topics Covered:**
- Set theory basics and operations
- Set membership and subset proofs
- Relations and their properties
- Equivalence relations
- Functions as relations

**Key Learning:** Work with sets and relations, fundamental concepts in mathematics.

### Tutorial 08: Advanced Tactics
**File:** `Tutorial08_AdvancedTactics.lean`

**Topics Covered:**
- Intermediate steps with `have` and `suffices`
- Proof by contradiction with `by_contra`
- Classical reasoning and excluded middle
- Case analysis and proof automation
- Combining multiple advanced techniques

**Key Learning:** Use sophisticated proof techniques for complex mathematical arguments.

## How to Use These Tutorials

1. **Sequential Learning:** Work through the tutorials in order, as each builds on concepts from previous ones.

2. **Interactive Learning:** Open each `.lean` file in VS Code with the Lean4 extension. You'll see:
   - Real-time feedback on your proofs
   - Error messages and suggestions
   - Goal states as you build proofs

3. **Practice Exercises:** Each tutorial contains exercises marked with `sorry`. Replace these with actual proofs to test your understanding.

4. **Experimentation:** Try modifying the examples to see how different approaches work.

## Common Patterns You'll Learn

- **Proof by Cases:** Using `cases` to handle different constructors
- **Proof by Induction:** Using `induction` for recursive structures
- **Proof by Contradiction:** Using `by_contra` to assume the negation
- **Rewriting:** Using `rw` and `simp` to transform expressions
- **Goal Decomposition:** Using `constructor` to break down complex goals

## Tips for Success

1. **Read the Error Messages:** Lean's error messages are very informative
2. **Use the Goal View:** Always check what you need to prove
3. **Start Simple:** Begin with the basic tactics before moving to advanced ones
4. **Practice Regularly:** Proof skills develop through consistent practice
5. **Don't Skip Exercises:** The exercises reinforce key concepts

## Additional Resources

- [Lean4 Manual](https://leanprover.github.io/lean4/doc/)
- [Mathematics in Lean](https://leanprover-community.github.io/mathematics_in_lean/)
- [Lean4 Theorem Proving](https://leanprover.github.io/theorem_proving_in_lean4/)
- [Mathlib Documentation](https://leanprover-community.github.io/mathlib4_docs/)

## Getting Help

If you get stuck:
1. Check the Lean4 documentation
2. Look at similar examples in the tutorial
3. Try breaking down complex proofs into smaller steps
4. Use `sorry` to skip parts and focus on specific goals
5. Ask questions in the Lean community forums

## What's Next?

After completing these tutorials, you'll be ready to:
- Explore Mathlib for advanced mathematical concepts
- Prove more complex theorems
- Contribute to mathematical formalization projects
- Apply formal verification to your own work

Happy proving! 🎯
