/-
# Tutorial 04: Natural Numbers and Induction in Lean4

This tutorial covers:
- Natural number arithmetic and properties
- Mathematical induction proofs
- Recursive function definitions
- Strong induction and well-founded recursion
- Common induction patterns

Key tactics: induction, cases, rw, simp, linarith
-/

import Mathlib.Tactic

/-
## 1. Natural Number Basics

Natural numbers (ℕ) are built from 0 and the successor function.
In Lean: 0, 1 = Nat.succ 0, 2 = Nat.succ 1, etc.
-/

-- Basic arithmetic properties
example (n : ℕ) : n + 0 = n := by rfl
example (n : ℕ) : 0 + n = n := by rfl
example (n : ℕ) : n * 0 = 0 := by rfl
example (n : ℕ) : 0 * n = 0 := by rfl
example (n : ℕ) : n * 1 = n := by rfl
example (n : ℕ) : 1 * n = n := by rfl

-- Successor properties
example (n : ℕ) : Nat.succ n = n + 1 := by rfl
example (n : ℕ) : n + 1 ≠ 0 := by simp

-- EXERCISE 1: Prove these basic properties
example (n : ℕ) : n + 1 = 1 + n := by
  sorry

example (n m : ℕ) : n + m = m + n := by
  sorry  -- Hint: use add_comm

/-
## 2. Mathematical Induction

Induction is the fundamental proof technique for natural numbers.
To prove ∀ n : ℕ, P(n), we prove:
1. Base case: P(0)
2. Inductive step: ∀ k, P(k) → P(k+1)
-/

-- Simple induction proof
example : ∀ n : ℕ, n + 0 = n := by
  intro n
  induction n with
  | zero => rfl  -- Base case: 0 + 0 = 0
  | succ k ih => 
    -- Inductive step: assume k + 0 = k, prove (k+1) + 0 = k+1
    rw [Nat.add_succ, ih]

-- More complex induction: sum formula
example : ∀ n : ℕ, 2 * (Finset.range (n + 1)).sum id = n * (n + 1) := by
  intro n
  induction n with
  | zero => simp
  | succ k ih =>
    rw [Finset.sum_range_succ, Finset.sum_range_id]
    rw [ih]
    ring

-- EXERCISE 2: Prove by induction
example : ∀ n : ℕ, (Finset.range (n + 1)).sum (fun i => i) = n * (n + 1) / 2 := by
  sorry

-- EXERCISE 3: Prove this induction
example : ∀ n : ℕ, 0 + 1 + 2 + ... + n = n * (n + 1) / 2 := by
  sorry  -- This is the same as exercise 2, but think about the pattern

/-
## 3. Recursive Function Definitions

We can define functions recursively on natural numbers.
-/

-- Factorial function
def factorial : ℕ → ℕ
  | 0 => 1
  | n + 1 => (n + 1) * factorial n

#eval factorial 5  -- Should be 120

-- Prove a property about factorial
example : factorial 3 = 6 := by rfl

-- Fibonacci sequence
def fib : ℕ → ℕ
  | 0 => 0
  | 1 => 1
  | n + 2 => fib n + fib (n + 1)

#eval fib 10  -- Should be 55

-- EXERCISE 4: Define the power function
def power (base : ℕ) : ℕ → ℕ := sorry

-- EXERCISE 5: Prove that power 2 3 = 8
example : power 2 3 = 8 := by
  sorry

/-
## 4. Induction on Recursive Functions

When proving properties about recursive functions, we often use induction.
-/

-- Prove factorial is always positive
example : ∀ n : ℕ, factorial n > 0 := by
  intro n
  induction n with
  | zero => simp [factorial]
  | succ k ih =>
    simp [factorial]
    exact Nat.mul_pos (Nat.succ_pos k) ih

-- Prove a property about Fibonacci
example : ∀ n : ℕ, fib (n + 1) + fib n = fib (n + 2) := by
  intro n
  cases n with
  | zero => simp [fib]
  | succ k => simp [fib]

-- EXERCISE 6: Prove factorial grows quickly
example : ∀ n : ℕ, n ≥ 1 → factorial n ≥ n := by
  sorry

-- EXERCISE 7: Prove a property about your power function
example (base : ℕ) : ∀ n : ℕ, power base (n + 1) = base * power base n := by
  sorry

/-
## 5. Strong Induction

Sometimes we need to assume the property holds for all smaller values.
-/

-- Strong induction principle (already available in Mathlib)
-- We can use Nat.strong_induction_on

-- Example: every number ≥ 2 has a prime factor
-- (We'll use a simplified version)
def has_prime_factor (n : ℕ) : Prop := 
  n ≥ 2 → ∃ p : ℕ, Nat.Prime p ∧ p ∣ n

example : ∀ n : ℕ, has_prime_factor n := by
  intro n
  apply Nat.strong_induction_on n
  intro k ih
  intro hk  -- k ≥ 2
  by_cases h : Nat.Prime k
  · use k
    exact ⟨h, dvd_refl k⟩
  · -- k is composite, so it has a proper divisor
    sorry  -- This requires more number theory

-- EXERCISE 8: Use strong induction to prove a simpler property
example : ∀ n : ℕ, n ≥ 1 → ∃ m : ℕ, m ≤ n ∧ m ≥ 1 := by
  sorry

/-
## 6. Cases on Natural Numbers

Sometimes we don't need full induction, just case analysis.
-/

-- Case analysis: n is either 0 or successor of something
example (n : ℕ) : n = 0 ∨ ∃ k : ℕ, n = k + 1 := by
  cases n with
  | zero => left; rfl
  | succ k => right; use k; rfl

-- Using cases in proofs
example (n : ℕ) : n * 0 = 0 := by
  cases n with
  | zero => rfl
  | succ k => rfl

-- EXERCISE 9: Use cases to prove this
example (n : ℕ) : n + 1 > n := by
  sorry

/-
## 7. Induction with Multiple Variables

We can do induction on multiple natural number variables.
-/

-- Induction on two variables
example (m n : ℕ) : m + n = n + m := by
  induction m with
  | zero => simp
  | succ k ih => 
    rw [Nat.succ_add, ih, Nat.add_succ]

-- More complex example: distributivity
example (a b c : ℕ) : a * (b + c) = a * b + a * c := by
  induction c with
  | zero => simp
  | succ k ih =>
    rw [Nat.add_succ, Nat.mul_succ, ih, Nat.mul_succ, Nat.add_assoc]

-- EXERCISE 10: Prove associativity of addition
example (a b c : ℕ) : (a + b) + c = a + (b + c) := by
  sorry

/-
## 8. Well-Founded Recursion

For more complex recursive patterns, we use well-founded recursion.
-/

-- Euclidean algorithm (GCD)
def gcd : ℕ → ℕ → ℕ
  | a, 0 => a
  | a, b + 1 => gcd (b + 1) (a % (b + 1))

-- The termination is guaranteed because a % (b + 1) < b + 1

-- EXERCISE 11: Define a function that counts digits
def count_digits : ℕ → ℕ := sorry
-- Hint: Use division by 10

/-
## 9. Induction Tactics and Patterns

Different ways to use induction in Lean.
-/

-- Standard induction
example (n : ℕ) : n + 0 = n := by
  induction n with
  | zero => rfl
  | succ k ih => rw [Nat.succ_add, ih]

-- Induction with custom induction principle
example (n : ℕ) : n + 0 = n := by
  apply Nat.rec
  · rfl  -- Base case
  · intro k ih  -- Inductive step
    rw [Nat.succ_add, ih]

-- EXERCISE 12: Use induction to prove
example : ∀ n : ℕ, 2^n ≥ n + 1 := by
  sorry

/-
## 10. Practice Problems

Test your understanding with these induction problems.
-/

-- Problem 1: Sum of first n odd numbers
example : ∀ n : ℕ, (Finset.range n).sum (fun i => 2 * i + 1) = n^2 := by
  sorry

-- Problem 2: Geometric series
example (r : ℕ) (hr : r ≠ 1) : ∀ n : ℕ, 
  (Finset.range (n + 1)).sum (fun i => r^i) = (r^(n + 1) - 1) / (r - 1) := by
  sorry

-- Problem 3: Factorial inequality
example : ∀ n : ℕ, n ≥ 4 → 2^n ≤ factorial n := by
  sorry

-- Problem 4: Fibonacci inequality
example : ∀ n : ℕ, n ≥ 1 → fib n ≤ 2^n := by
  sorry

-- Problem 5: Divisibility property
example : ∀ n : ℕ, 3 ∣ (n^3 - n) := by
  sorry

-- Problem 6: Prove this recursive equation
def T : ℕ → ℕ
  | 0 => 1
  | n + 1 => 2 * T n + 1

example : ∀ n : ℕ, T n = 2^(n + 1) - 1 := by
  sorry

/-
## Summary

In this tutorial, you learned:
1. Natural number arithmetic and basic properties
2. Mathematical induction with base case and inductive step
3. Recursive function definitions and their properties
4. Strong induction for more complex proofs
5. Case analysis on natural numbers
6. Induction with multiple variables
7. Well-founded recursion for complex patterns
8. Various induction tactics and patterns
9. Common induction proof techniques

Next tutorial: Lists and Data Structures.
-/
